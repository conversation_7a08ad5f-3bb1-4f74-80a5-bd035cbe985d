"""Jira Definition of Done (DoD) prompt templates."""

DEFINITION_OF_DONE_SYSTEM_MESSAGE = """You are an experienced software development engineer and quality assurance specialist. Your main task is to analyze development tickets (feature development, bug fixing, user story implementation, development narratives) and generate a comprehensive Definition of Done (DoD) checklist to ensure work meets completion criteria.

Your analysis should cover:
- Feature flag usage assessment
- Acceptance criteria validation  
- Test plan documentation and risk assessment
- Code review requirements
- Automated testing coverage (unit, integration, API, BDD tests)
- Technical documentation updates
- Security and privacy compliance
- Pull request size and quality checks
- Proper ticket tagging and closure procedures

Accuracy and thoroughness are critical. Ensure your DoD summary is comprehensive and actionable so that development teams can validate completion criteria effectively."""

def generate_definition_of_done_prompt(task_description: str) -> str:
    """Generate Definition of Done prompt for development tickets."""
    return f"""Scenario: As an experienced software development engineer and QA specialist, you need to analyze the given development ticket and generate a simple, clean Definition of Done (DoD) checklist.

Task Description: "{task_description}"

**CRITICAL OUTPUT REQUIREMENT**: You MUST generate your response as a single string that contains literal newline characters (\\n) for proper formatting. The output will be processed as text, so actual newline characters are essential for correct display.

**MANDATORY NEWLINE CHARACTER USAGE**:
- Use \\n for single line breaks
- Use \\n\\n for double line breaks (empty lines)
- Your output must be a continuous string with embedded \\n characters
- Do NOT use visual line breaks - use literal \\n characters

**EXACT OUTPUT FORMAT WITH NEWLINE CHARACTERS**:
Your response must follow this EXACT pattern with literal \\n characters:

Summary: [content]\\n\\n\\nDoD:\\n\\n- [item1]\\n- [item2]\\n- [item3]\\n- [item4]\\n- [item5]\\n- [item6]\\n- [item7]\\n\\n\\nDisclaimer / Discussion notes:\\n\\n[content]

**SPECIFIC FORMATTING REQUIREMENTS**:
1. Start with "Summary: " followed by 1-2 sentences, then \\n\\n\\n
2. Add "DoD:" followed by \\n\\n
3. List 7-9 bullet points, each starting with "- " and ending with \\n
4. After the last bullet point, add \\n\\n\\n
5. Add "Disclaimer / Discussion notes:" followed by \\n\\n
6. End with 2-3 practical notes

**CONTENT REQUIREMENTS**:
- Include these 4 core items first:
  * "- Ensure that end-to-end customer workflows are covered\\n"
  * "- Automated tests covering end-to-end customer workflows\\n"
  * "- Ensure that all the fields within Jira have been filled out properly\\n"
  * "- Evaluate risk / impacted areas / validation as if the code was being pushed immediately to production\\n"
- Add 3-5 task-specific validation criteria based on the task description
- Make validation criteria specific and actionable
- Include practical disclaimer notes relevant to the task

**EXAMPLE OUTPUT STRUCTURE** (showing literal \\n characters):
Summary: Implement user authentication feature with secure login and session management.\\n\\n\\nDoD:\\n\\n- Ensure that end-to-end customer workflows are covered\\n- Automated tests covering end-to-end customer workflows\\n- Ensure that all the fields within Jira have been filled out properly\\n- Evaluate risk / impacted areas / validation as if the code was being pushed immediately to production\\n- Authentication endpoints tested with valid and invalid credentials\\n- Session management and timeout functionality validated\\n- Security review completed for authentication flow\\n\\n\\nDisclaimer / Discussion notes:\\n\\n- Coordinate with security team for penetration testing. Consider implementing rate limiting for login attempts. Ensure proper error handling without exposing sensitive information.

**CRITICAL REMINDERS**:
- Your entire response must be ONE continuous string with \\n characters
- Do NOT use actual line breaks in your response
- Use literal \\n characters for all line breaks
- Follow the exact pattern shown above
- Include exactly 3 \\n characters between major sections
- Include exactly 2 \\n characters after section headers
"""

DEFINITION_OF_DONE_PROMPT = {
    "system_message": DEFINITION_OF_DONE_SYSTEM_MESSAGE,
    "generate_prompt": generate_definition_of_done_prompt,
    "suggestions": ["Review DoD criteria thoroughly", "Assess all quality gates", "Validate completion requirements"]
}
