import { marked } from "marked"
import React, { useEffect, useState } from "react"

import { useNotification } from "~components/common/notification"
import { useJiraDoDefinitionMessaging } from "~hook/use-api-messaging"

import { SparklesIcon } from "../../../lib/icons/heroicon"

export const GenerateDoDefinitionButton = () => {
  const { addNotification } = useNotification()
  const { execute, loading, error } = useJiraDoDefinitionMessaging()
  const [streamingContent, setStreamingContent] = useState("")

  // Monitor error changes and display notifications
  useEffect(() => {
    if (error) {
      console.error("Jira API Error:", error)
      addNotification({
        type: "error",
        title: "Generation Failed",
        message: error
      })
    }
  }, [error, addNotification])

  const handleClick = async () => {
    const $commentIframe = document.querySelector("#mce_0_ifr")
    if (!$commentIframe) {
      const $commentEditor = document.querySelector("#footer-comment-button")
      if (!$commentEditor) return
      const clickEvent = new MouseEvent("click", {
        bubbles: true, // Event bubbling
        cancelable: true, // Event cancellable
        view: window // Event view (usually window)
      })
      $commentEditor.dispatchEvent(clickEvent)
    }

    const $commentField = document.querySelector("textarea#comment")
    if (!$commentField) return
    const description = document.querySelector("#description-val")?.textContent

    if (!description?.trim()) {
      addNotification({
        type: "warning",
        title: "Warning",
        message: "Task description not found. Please ensure the page is fully loaded."
      })
      return
    }

    await handleGenerateDoD(description)
  }

  const handleGenerateDoD = async (description: string) => {
    if (loading) return

    console.log("🚀 Starting Jira Definition of Done generation (streaming response)...")
    console.log("Task description:", description)

    // First test health check to verify messaging system is working
    console.log("🔍 Testing messaging system connection...")
    try {
      const healthCheck = await fetch("http://localhost:8000/api/v1/ai/health")
      console.log("🔍 Backend API health check:", healthCheck.status)
    } catch (error) {
      console.error("🔍 Backend API connection failed:", error)
      addNotification({
        type: "error",
        title: "Connection Failed",
        message: "Unable to connect to backend API server. Please ensure the server is running."
      })
      return
    }

    // Clear previous streaming content
    setStreamingContent("")

    addNotification({
      type: "info",
      title: "Generating",
      message: "Generating Definition of Done summary in real-time..."
    })

    // Use API to call backend service with streaming response by default
    const result = await execute({
      task_description: description
    }, {
      onChunk: (chunk: string, fullText: string) => {
        console.log("📝 Received streaming content:", chunk)
        setStreamingContent(fullText)
        // Update comment area in real-time
        setCommentAreaRealtime(fullText)
      }
    })

    // If successful, ensure final content is set
    if (result) {
      console.log("✅ Generation successful:", result)
      const finalContent = result.generated_content || streamingContent
      // Ensure final content is properly set
      await setCommentArea(finalContent)
      addNotification({
        type: "info",
        title: "Generation Successful",
        message: "Definition of Done summary has been generated and filled into the form"
      })
      setStreamingContent("")
    } else {
      console.error("❌ Generation failed, result is empty")
    }
  }

  // Real-time update comment area (for streaming response)
  const setCommentAreaRealtime = async (text: string) => {
    console.log("🔄 Setting realtime content:", text.substring(0, 100) + "...")
    console.log("🔄 Text contains newlines:", text.includes('\n'))

    const $commentField = document.querySelector(
      "textarea#comment"
    ) as HTMLTextAreaElement
    if ($commentField) {
      $commentField.value = text
      // Trigger input event
      const event = new Event('input', { bubbles: true })
      $commentField.dispatchEvent(event)
      console.log("✅ Textarea updated with content length:", text.length)
    }

    // Also update rich text editor
    const $commentEditor = document.querySelector(
      "#mce_0_ifr"
    ) as HTMLIFrameElement
    if ($commentEditor) {
      const iframeDocument =
        $commentEditor.contentDocument || $commentEditor.contentWindow?.document
      const targetElement = iframeDocument?.getElementById("tinymce")
      if (targetElement) {
        try {
          // The backend now returns properly formatted text with \n characters
          // We need to convert this to proper markdown/HTML format

          // First, ensure we have the proper structure for markdown processing
          let processedText = text

          // Only add formatting if the text doesn't already have proper structure
          if (!text.includes('\n\n\n')) {
            // Legacy fallback - add proper spacing if needed
            processedText = text
              .replace(/\n\n/g, '\n\n')  // Preserve double line breaks
              .replace(/\n([+-])/g, '\n\n$1')  // Add space before list items
              .replace(/(Summary:|DoD:|Disclaimer \/ Discussion notes:)/g, '\n\n$1')  // Add space before section headers
              .replace(/(\n- [^\n]+)(\n[A-Z])/g, '$1\n$2')  // Add space after bullet lists before new sections
          }

          console.log("📝 Processing text for TinyMCE:", processedText.substring(0, 200) + "...")

          // Configure marked options for better HTML structure
          const comment = await marked(processedText, {
            breaks: true,  // Convert \n to <br>
            gfm: true,     // GitHub Flavored Markdown
            pedantic: false
          })

          console.log("🎯 Generated HTML:", comment.substring(0, 200) + "...")

          // Replace the entire content instead of just the paragraph
          targetElement.innerHTML = comment

          // Verify the content was set correctly
          console.log("✅ TinyMCE content updated successfully")
        } catch (e) {
          console.warn("❌ Markdown conversion failed:", e)
          // Fallback: set as plain text with line breaks converted to <br>
          const fallbackHtml = `<p>${text.replace(/\n/g, '<br>')}</p>`
          targetElement.innerHTML = fallbackHtml
          console.log("🔄 Used fallback HTML formatting")
        }
      }
    }
  }

  const setCommentArea = async (text: string) => {
    console.log("🎯 Setting final content:", text.substring(0, 100) + "...")
    console.log("🎯 Final text contains newlines:", text.includes('\n'))
    console.log("🎯 Final text length:", text.length)

    const $commentField = document.querySelector(
      "textarea#comment"
    ) as HTMLTextAreaElement
    if (!$commentField) {
      console.warn("❌ Comment text field not found")
      return
    }

    // Set the textarea value - this should display newlines correctly
    $commentField.value = text
    console.log("✅ Final textarea value set:", $commentField.value.substring(0, 100) + "...")

    // Trigger input event
    const event = new Event('input', { bubbles: true })
    $commentField.dispatchEvent(event)

    const $commentEditor = document.querySelector(
      "#mce_0_ifr"
    ) as HTMLIFrameElement
    if (!$commentEditor) {
      console.warn("❌ TinyMCE editor not found")
      return
    }

    const iframeDocument =
      $commentEditor.contentDocument || $commentEditor.contentWindow?.document
    const targetElement = iframeDocument?.getElementById("tinymce")
    if (targetElement) {
      try {
        // The backend now returns properly formatted text with \n characters
        // We need to convert this to proper markdown/HTML format

        // First, ensure we have the proper structure for markdown processing
        let processedText = text

        // Only add formatting if the text doesn't already have proper structure
        if (!text.includes('\n\n\n')) {
          // Legacy fallback - add proper spacing if needed
          processedText = text
            .replace(/\n\n/g, '\n\n')  // Preserve double line breaks
            .replace(/\n([+-])/g, '\n\n$1')  // Add space before list items
            .replace(/(Summary:|DoD:|Disclaimer \/ Discussion notes:)/g, '\n\n$1')  // Add space before section headers
            .replace(/(\n- [^\n]+)(\n[A-Z])/g, '$1\n$2')  // Add space after bullet lists before new sections
        }

        console.log("📝 Final processing text for TinyMCE:", processedText.substring(0, 200) + "...")

        // Configure marked options for better HTML structure
        const comment = await marked(processedText, {
          breaks: true,  // Convert \n to <br>
          gfm: true,     // GitHub Flavored Markdown
          pedantic: false
        })

        console.log("🎯 Final generated HTML:", comment.substring(0, 200) + "...")

        // Replace the entire content instead of just the paragraph
        targetElement.innerHTML = comment

        // Verify the final content was set correctly
        console.log("✅ Final TinyMCE content updated successfully")
        console.log("📋 Final TinyMCE innerHTML length:", targetElement.innerHTML.length)
      } catch (e) {
        console.warn("❌ Final markdown conversion failed:", e)
        // Fallback: set as plain text with line breaks converted to <br>
        const fallbackHtml = `<p>${text.replace(/\n/g, '<br>')}</p>`
        targetElement.innerHTML = fallbackHtml
        console.log("🔄 Used final fallback HTML formatting")
      }
    }
  }

  return (
    <div
      className="aui-buttons"
      onClick={handleClick}
      style={{ cursor: loading ? "not-allowed" : "pointer", opacity: loading ? 0.6 : 1 }}
      title="Generate a Definition of Done summary based on the ticket description to help validate if the work meets completion criteria. Includes feature flag assessment, test coverage validation, code review requirements, and quality gates."
    >
      <a
        title="Generate Definition of Done Summary (Real-time Generation)"
        className="aui-button toolbar-trigger issueaction-comment-issue add-issue-comment inline-comment"
        style={{ display: "flex", alignItems: "center" }}>
        <SparklesIcon style={{ width: 20, marginRight: 5 }} />
        <span className="trigger-label">
          {loading ? "Generating DoD..." : "Generate DoD Summary"}
        </span>
      </a>
    </div>
  )
}
